package admin

import (
	"go-mail/internal/api/handlers"
	"go-mail/internal/api/middleware"
	"go-mail/internal/auth"
	"go-mail/internal/database"
	"go-mail/internal/scheduler"
	"go-mail/internal/services"
	"log/slog"

	"github.com/gin-gonic/gin"
)

// AdminAPI 管理后台API控制器
type AdminAPI struct {
	services  *services.Services
	auth      *auth.Auth
	database  *database.Database
	scheduler *scheduler.Scheduler
	logger    *slog.Logger
}

// NewAdminAPI 创建管理后台API控制器
func NewAdminAPI(services *services.Services, auth *auth.Auth, database *database.Database, scheduler *scheduler.Scheduler, logger *slog.Logger) *AdminAPI {
	return &AdminAPI{
		services:  services,
		auth:      auth,
		database:  database,
		scheduler: scheduler,
		logger:    logger,
	}
}

// RegisterRoutes 注册管理后台路由
func (a *AdminAPI) RegisterRoutes(v1 *gin.RouterGroup) {
	// 管理后台认证路由
	a.registerAuthRoutes(v1)

	// 激活码管理路由
	a.registerActivationRoutes(v1)

	// 系统监控路由
	a.registerMonitorRoutes(v1)

	// 系统配置路由
	a.registerConfigRoutes(v1)

	// 系统管理路由
	a.registerSystemRoutes(v1)

	// 邮箱管理路由
	a.registerMailboxRoutes(v1)
}

// registerAuthRoutes 注册认证路由
func (a *AdminAPI) registerAuthRoutes(v1 *gin.RouterGroup) {
	authGroup := v1.Group("/auth")
	{
		authHandler := handlers.NewAuthHandler(a.services.Auth, a.database)
		a.logger.Info("注册认证路由", "prefix", "/api/v1/auth")
		authGroup.POST("/login", authHandler.Login)
		authGroup.POST("/refresh", authHandler.RefreshToken)
		authGroup.POST("/logout", middleware.JWTAuth(a.auth), authHandler.Logout)
		authGroup.GET("/validate", middleware.JWTAuth(a.auth), authHandler.ValidateToken)
		authGroup.GET("/me", middleware.JWTAuth(a.auth), authHandler.GetProfile)
		a.logger.Info("认证路由注册完成", "routes", []string{
			"POST /api/v1/auth/login",
			"POST /api/v1/auth/refresh",
			"POST /api/v1/auth/logout",
			"GET /api/v1/auth/validate",
			"GET /api/v1/auth/me",
		})
	}
}

// registerActivationRoutes 注册激活码管理路由
func (a *AdminAPI) registerActivationRoutes(v1 *gin.RouterGroup) {
	activationGroup := v1.Group("/activation")
	activationGroup.Use(middleware.JWTAuth(a.auth))
	{
		activationHandler := handlers.NewActivationHandler(a.services.Activation)
		activationGroup.POST("/generate", activationHandler.GenerateCodes)
		activationGroup.POST("/verify", activationHandler.VerifyCode)
		activationGroup.GET("/list", activationHandler.ListCodes)
		activationGroup.GET("/:id", activationHandler.GetCodeInfo)
		activationGroup.DELETE("/:id", activationHandler.DeleteCode)
		activationGroup.POST("/batch-delete", activationHandler.BatchDeleteCodes)
		activationGroup.PUT("/:id/expire", activationHandler.ExpireCode)
		activationGroup.GET("/export", activationHandler.ExportCodes)
		activationGroup.GET("/stats", activationHandler.GetStats)
	}
}

// registerMonitorRoutes 注册系统监控路由
func (a *AdminAPI) registerMonitorRoutes(v1 *gin.RouterGroup) {
	monitorGroup := v1.Group("/monitor")
	monitorGroup.Use(middleware.JWTAuth(a.auth))
	{
		monitorHandler := handlers.NewMonitorHandler(a.services.Monitor, a.scheduler)
		monitorGroup.GET("/statistics", monitorHandler.GetStatistics)
		monitorGroup.GET("/accounts", monitorHandler.GetAccountStatus)
		monitorGroup.GET("/sessions", monitorHandler.GetSessionStatus)
		monitorGroup.GET("/logs", monitorHandler.GetLogs)
		monitorGroup.GET("/tasks", monitorHandler.GetTaskStatus)
		monitorGroup.POST("/tasks/:task/:action", monitorHandler.ControlTask)
	}
}

// registerConfigRoutes 注册系统配置路由
func (a *AdminAPI) registerConfigRoutes(v1 *gin.RouterGroup) {
	configGroup := v1.Group("/config")
	configGroup.Use(middleware.JWTAuth(a.auth))
	{
		configHandler := handlers.NewConfigHandler(a.services.Config)
		configGroup.GET("", configHandler.GetConfig)
		configGroup.PUT("", configHandler.UpdateConfig)
	}
}

// registerSystemRoutes 注册系统管理路由
func (a *AdminAPI) registerSystemRoutes(v1 *gin.RouterGroup) {
	systemGroup := v1.Group("/system")
	systemGroup.Use(middleware.JWTAuth(a.auth))
	{
		systemHandler := handlers.NewSystemHandler(a.database, a.scheduler)
		systemGroup.POST("/cleanup", systemHandler.CleanupSystem)
		systemGroup.POST("/backup", systemHandler.BackupSystem)
		systemGroup.GET("/version", systemHandler.GetVersionInfo)
	}
}

// registerMailboxRoutes 注册邮箱管理路由
func (a *AdminAPI) registerMailboxRoutes(v1 *gin.RouterGroup) {
	mailboxGroup := v1.Group("/mailbox")
	mailboxGroup.Use(middleware.JWTAuth(a.auth))
	mailboxGroup.Use(middleware.SecurityHeaders())
	mailboxGroup.Use(middleware.OperationAudit())
	{
		mailboxHandler := handlers.NewMailboxManagementHandler(a.services.MailboxManagement)
		a.logger.Info("注册邮箱管理路由", "prefix", "/api/v1/mailbox")

		// 创建频率限制器
		rateLimiter := middleware.NewRateLimiter()

		// 批量操作（应用严格的频率和并发限制）
		mailboxGroup.POST("/batch-import",
			rateLimiter.ImportRateLimit(),
			middleware.BatchImportSizeLimit(),
			middleware.BatchOperationConcurrencyLimit(),
			mailboxHandler.BatchImportAccounts)

		mailboxGroup.POST("/verify",
			rateLimiter.VerificationRateLimit(),
			middleware.VerificationConcurrencyLimit(),
			mailboxHandler.StartVerificationTask)

		mailboxGroup.GET("/batch-operation/:operation_id", mailboxHandler.GetBatchOperationStatus)

		// 账户管理
		mailboxGroup.GET("/accounts", mailboxHandler.GetAccountsList)
		mailboxGroup.DELETE("/accounts/:id", mailboxHandler.DeleteAccount)
		mailboxGroup.PUT("/accounts/:id/status", mailboxHandler.ToggleAccountStatus)
		mailboxGroup.POST("/batch-delete", mailboxHandler.BatchDeleteAccounts)
		mailboxGroup.POST("/batch-disable", mailboxHandler.BatchDisableAccounts)

		// 任务控制（应用频率限制）
		mailboxGroup.POST("/task-control",
			rateLimiter.BatchOperationRateLimit(),
			mailboxHandler.ControlTask)

		mailboxGroup.GET("/scheduler-status", mailboxHandler.GetTaskSchedulerStatus)

		// 统计信息
		mailboxGroup.GET("/statistics", mailboxHandler.GetMailboxStatistics)

		// 任务日志管理
		taskLogHandler := handlers.NewTaskLogHandler(a.services.TaskLog)
		mailboxGroup.GET("/task-logs", taskLogHandler.GetTaskLogs)
		mailboxGroup.GET("/task-logs/:id/details", taskLogHandler.GetTaskLogDetail)
		mailboxGroup.POST("/task-logs", taskLogHandler.CreateTaskLog)
		mailboxGroup.PUT("/task-logs/:task_id", taskLogHandler.UpdateTaskLog)

		a.logger.Info("邮箱管理路由注册完成", "routes", []string{
			"POST /api/v1/mailbox/batch-import",
			"POST /api/v1/mailbox/verify",
			"GET /api/v1/mailbox/batch-operation/:operation_id",
			"GET /api/v1/mailbox/accounts",
			"DELETE /api/v1/mailbox/accounts/:id",
			"PUT /api/v1/mailbox/accounts/:id/status",
			"POST /api/v1/mailbox/batch-delete",
			"POST /api/v1/mailbox/batch-disable",
			"POST /api/v1/mailbox/task-control",
			"GET /api/v1/mailbox/scheduler-status",
			"GET /api/v1/mailbox/statistics",
			"GET /api/v1/mailbox/task-logs",
			"GET /api/v1/mailbox/task-logs/:id/details",
			"POST /api/v1/mailbox/task-logs",
			"PUT /api/v1/mailbox/task-logs/:task_id",
		})
	}
}
