package database

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// BatchOperation 批量操作记录
type BatchOperation struct {
	ID              int        `db:"id" json:"id"`
	OperationID     string     `db:"operation_id" json:"operation_id"`
	OperationType   string     `db:"operation_type" json:"operation_type"` // import, verify, disable, enable
	Status          string     `db:"status" json:"status"`                 // pending, running, completed, failed, cancelled
	TotalCount      int        `db:"total_count" json:"total_count"`
	ProcessedCount  int        `db:"processed_count" json:"processed_count"`
	SuccessCount    int        `db:"success_count" json:"success_count"`
	FailedCount     int        `db:"failed_count" json:"failed_count"`
	ErrorMessage    string     `db:"error_message" json:"error_message"`
	CreatedBy       string     `db:"created_by" json:"created_by"`
	CreatedAt       time.Time  `db:"created_at" json:"created_at"`
	StartedAt       *time.Time `db:"started_at" json:"started_at"`
	CompletedAt     *time.Time `db:"completed_at" json:"completed_at"`
	ProgressData    JSONMap    `db:"progress_data" json:"progress_data"`
	OperationParams JSONMap    `db:"operation_params" json:"operation_params"`
}

// MailboxVerificationTask 邮箱验证任务
type MailboxVerificationTask struct {
	ID                 int        `db:"id" json:"id"`
	TaskID             string     `db:"task_id" json:"task_id"`
	BatchOperationID   string     `db:"batch_operation_id" json:"batch_operation_id"`
	AccountEmail       string     `db:"account_email" json:"account_email"`
	Status             string     `db:"status" json:"status"` // pending, running, success, failed, skipped
	VerificationType   string     `db:"verification_type" json:"verification_type"`
	StartedAt          *time.Time `db:"started_at" json:"started_at"`
	CompletedAt        *time.Time `db:"completed_at" json:"completed_at"`
	DurationMs         int        `db:"duration_ms" json:"duration_ms"`
	ErrorMessage       string     `db:"error_message" json:"error_message"`
	VerificationResult JSONMap    `db:"verification_result" json:"verification_result"`
	RetryCount         int        `db:"retry_count" json:"retry_count"`
	MaxRetries         int        `db:"max_retries" json:"max_retries"`
	CreatedAt          time.Time  `db:"created_at" json:"created_at"`
}

// TaskSchedulerStatus 任务调度器状态
type TaskSchedulerStatus struct {
	ID                 int        `db:"id" json:"id"`
	TaskName           string     `db:"task_name" json:"task_name"`
	TaskType           string     `db:"task_type" json:"task_type"`
	Status             string     `db:"status" json:"status"` // stopped, running, paused, error
	CurrentOperationID string     `db:"current_operation_id" json:"current_operation_id"`
	LastRunAt          *time.Time `db:"last_run_at" json:"last_run_at"`
	NextRunAt          *time.Time `db:"next_run_at" json:"next_run_at"`
	RunCount           int        `db:"run_count" json:"run_count"`
	ErrorCount         int        `db:"error_count" json:"error_count"`
	LastError          string     `db:"last_error" json:"last_error"`
	ConfigData         JSONMap    `db:"config_data" json:"config_data"`
	CreatedAt          time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt          time.Time  `db:"updated_at" json:"updated_at"`
}

// MailboxStatistics 邮箱统计信息
type MailboxStatistics struct {
	ID                      int       `db:"id" json:"id"`
	StatDate                time.Time `db:"stat_date" json:"stat_date"`
	TotalAccounts           int       `db:"total_accounts" json:"total_accounts"`
	ActiveAccounts          int       `db:"active_accounts" json:"active_accounts"`
	VerifiedAccounts        int       `db:"verified_accounts" json:"verified_accounts"`
	FailedAccounts          int       `db:"failed_accounts" json:"failed_accounts"`
	DisabledAccounts        int       `db:"disabled_accounts" json:"disabled_accounts"`
	NewImportsToday         int       `db:"new_imports_today" json:"new_imports_today"`
	VerificationSuccessRate float64   `db:"verification_success_rate" json:"verification_success_rate"`
	AvgVerificationTimeMs   int       `db:"avg_verification_time_ms" json:"avg_verification_time_ms"`
	CreatedAt               time.Time `db:"created_at" json:"created_at"`
}

// ExtendedAccount 扩展的账户信息（包含新增字段）
type ExtendedAccount struct {
	ID                   int         `db:"id" json:"id"`
	Email                string      `db:"email" json:"email"`
	Password             string      `db:"password" json:"password"`
	CookieData           string      `db:"cookie_data" json:"cookie_data"`
	LoginStatus          string      `db:"login_status" json:"login_status"`
	LastLoginTime        *time.Time  `db:"last_login_time" json:"last_login_time"`
	EmailStatus          string      `db:"email_status" json:"email_status"`
	UsageStatus          string      `db:"usage_status" json:"usage_status"`
	UsageID              string      `db:"usage_id" json:"usage_id"`
	SessionID            string      `db:"session_id" json:"session_id"`
	JSessionID           string      `db:"jsession_id" json:"jsession_id"`
	NavigatorSID         string      `db:"navigator_sid" json:"navigator_sid"`
	CreatedAt            time.Time   `db:"created_at" json:"created_at"`
	UpdatedAt            time.Time   `db:"updated_at" json:"updated_at"`
	BatchImportID        string      `db:"batch_import_id" json:"batch_import_id"`
	VerificationStatus   string      `db:"verification_status" json:"verification_status"`
	LastVerificationTime *time.Time  `db:"last_verification_time" json:"last_verification_time"`
	VerificationError    string      `db:"verification_error" json:"verification_error"`
	IsDisabled           bool        `db:"is_disabled" json:"is_disabled"`
	ImportSource         string      `db:"import_source" json:"import_source"`
	Tags                 StringSlice `db:"tags" json:"tags"`
}

// BatchImportRequest 批量导入请求
type BatchImportRequest struct {
	Accounts    []AccountImportData `json:"accounts"`
	Source      string              `json:"source"`
	Tags        []string            `json:"tags"`
	AutoVerify  bool                `json:"auto_verify"`
	Description string              `json:"description"`
}

// AccountImportData 账户导入数据
type AccountImportData struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// BatchOperationResponse 批量操作响应
type BatchOperationResponse struct {
	OperationID string `json:"operation_id"`
	Status      string `json:"status"`
	Message     string `json:"message"`
	TotalCount  int    `json:"total_count"`
}

// VerificationTaskRequest 验证任务请求
type VerificationTaskRequest struct {
	AccountEmails    []string `json:"account_emails"`
	VerificationType string   `json:"verification_type"`
	ConcurrentLimit  int      `json:"concurrent_limit"`
	RetryLimit       int      `json:"retry_limit"`
}

// TaskControlRequest 任务控制请求
type TaskControlRequest struct {
	Action string `json:"action"` // start, pause, stop, reset
	TaskID string `json:"task_id"`
}

// MailboxFilterRequest 邮箱筛选请求
type MailboxFilterRequest struct {
	Status             []string   `json:"status"`
	VerificationStatus []string   `json:"verification_status"`
	ImportSource       []string   `json:"import_source"`
	Tags               []string   `json:"tags"`
	DateRange          *DateRange `json:"date_range"`
	Page               int        `json:"page"`
	PageSize           int        `json:"page_size"`
	SortBy             string     `json:"sort_by"`
	SortOrder          string     `json:"sort_order"`
}

// DateRange 日期范围
type DateRange struct {
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
}

// PaginatedResponse 分页响应
type PaginatedResponse[T any] struct {
	Items      []T  `json:"items"`
	Total      int  `json:"total"`
	Page       int  `json:"page"`
	PageSize   *int `json:"page_size"`
	TotalPages *int `json:"total_pages"`
}

// JSONMap 自定义JSON映射类型
type JSONMap map[string]interface{}

// Scan 实现 sql.Scanner 接口
func (m *JSONMap) Scan(value interface{}) error {
	if value == nil {
		*m = make(JSONMap)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, m)
}

// Value 实现 driver.Valuer 接口
func (m JSONMap) Value() (driver.Value, error) {
	if len(m) == 0 {
		return nil, nil
	}
	return json.Marshal(m)
}

// StringSlice 自定义字符串切片类型
type StringSlice []string

// Scan 实现 sql.Scanner 接口
func (s *StringSlice) Scan(value interface{}) error {
	if value == nil {
		*s = StringSlice{}
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, s)
}

// Value 实现 driver.Valuer 接口
func (s StringSlice) Value() (driver.Value, error) {
	if len(s) == 0 {
		return nil, nil
	}
	return json.Marshal(s)
}
