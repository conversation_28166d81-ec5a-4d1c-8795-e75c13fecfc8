package handlers

import (
	"go-mail/internal/database"
	"go-mail/internal/services"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService *services.AuthService
	db          *database.Database
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(authService *services.AuthService, db *database.Database) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		db:          db,
	}
}

// Login 管理员登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req database.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证必填字段
	if req.Username == "" || req.Password == "" {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "用户名和密码不能为空",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 执行登录
	tokenResponse, err := h.authService.Login(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, database.APIResponse{
			Code:      401,
			Message:   "登录失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "登录成功",
		Data:      tokenResponse,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// RefreshToken 刷新令牌
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	// 从请求体获取刷新令牌
	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 刷新令牌
	tokenResponse, err := h.authService.RefreshToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, database.APIResponse{
			Code:      401,
			Message:   "刷新令牌失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "令牌刷新成功",
		Data:      tokenResponse,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// Logout 登出
func (h *AuthHandler) Logout(c *gin.Context) {
	// 从上下文获取用户信息
	username := c.GetString("username")

	// 执行登出逻辑（如将令牌加入黑名单）
	err := h.authService.Logout("")
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "登出失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "登出成功",
		Data: gin.H{
			"username":    username,
			"logout_time": time.Now().Format(time.RFC3339),
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// GetProfile 获取用户信息
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID := c.GetInt("user_id")

	user, err := h.authService.ValidateUser(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, database.APIResponse{
			Code:      404,
			Message:   "用户不存在",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "获取成功",
		Data:      user,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// ValidateToken 验证令牌有效性
func (h *AuthHandler) ValidateToken(c *gin.Context) {
	// 如果能到达这里，说明JWT中间件已经验证了令牌
	userID := c.GetInt("user_id")
	username := c.GetString("username")
	role := c.GetString("role")

	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "令牌有效",
		Data: gin.H{
			"valid":    true,
			"user_id":  userID,
			"username": username,
			"role":     role,
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// UpdatePassword 更新密码
func (h *AuthHandler) UpdatePassword(c *gin.Context) {
	userID := c.GetInt("user_id")

	var req struct {
		OldPassword string `json:"old_password" binding:"required"`
		NewPassword string `json:"new_password" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证新密码强度
	if len(req.NewPassword) < 6 {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "新密码长度不能少于6个字符",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 更新密码
	err := h.authService.UpdateUserPassword(userID, req.OldPassword, req.NewPassword)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "密码更新失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:      200,
		Message:   "密码更新成功",
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// CreateUser 创建用户
func (h *AuthHandler) CreateUser(c *gin.Context) {
	var req struct {
		Username string `json:"username" binding:"required"`
		Password string `json:"password" binding:"required"`
		Role     string `json:"role" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 验证角色
	if req.Role != "admin" && req.Role != "operator" {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "无效的用户角色",
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	// 创建用户
	user, err := h.authService.CreateUser(req.Username, req.Password, req.Role)
	if err != nil {
		c.JSON(http.StatusBadRequest, database.APIResponse{
			Code:      400,
			Message:   "用户创建失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusCreated, database.APIResponse{
		Code:      201,
		Message:   "用户创建成功",
		Data:      user,
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}

// ListUsers 获取用户列表
func (h *AuthHandler) ListUsers(c *gin.Context) {
	users, err := h.authService.ListUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, database.APIResponse{
			Code:      500,
			Message:   "获取用户列表失败",
			Error:     err.Error(),
			Timestamp: time.Now().Format(time.RFC3339),
			RequestID: c.GetString("request_id"),
		})
		return
	}

	c.JSON(http.StatusOK, database.APIResponse{
		Code:    200,
		Message: "获取成功",
		Data: gin.H{
			"users": users,
			"total": len(users),
		},
		Timestamp: time.Now().Format(time.RFC3339),
		RequestID: c.GetString("request_id"),
	})
}
